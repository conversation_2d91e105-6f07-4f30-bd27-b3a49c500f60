// Simple test script to verify server startup
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env') });

import app from './app.js';

const PORT = process.env.PORT || 5000;

console.log('🧪 Testing server startup...');
console.log('Environment variables check:');
console.log('- PORT:', process.env.PORT);
console.log('- MONGO_URI:', process.env.MONGO_URI ? '✅ Set' : '❌ Missing');
console.log('- JWT_SECRET:', process.env.JWT_SECRET ? '✅ Set' : '❌ Missing');
console.log('- CLOUDINARY_CLOUD_NAME:', process.env.CLOUDINARY_CLOUD_NAME ? '✅ Set' : '❌ Missing');

// Test basic app functionality
try {
    console.log('✅ App imported successfully');
    console.log('🚀 Server configuration appears to be correct');
    console.log('💡 You can now start the server with: node server.js');
} catch (error) {
    console.error('❌ Error importing app:', error.message);
    process.exit(1);
}
