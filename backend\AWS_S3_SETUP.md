# AWS S3 Setup Guide

This guide will help you set up AWS S3 for file storage in your LMS application.

## Prerequisites

1. AWS Account
2. AWS CLI installed (optional but recommended)

## Step 1: Create an S3 Bucket

1. Log in to your AWS Console
2. Navigate to S3 service
3. Click "Create bucket"
4. Choose a unique bucket name (e.g., `your-lms-bucket-name`)
5. Select your preferred region (e.g., `us-east-1`)
6. Configure bucket settings:
   - **Block Public Access**: Uncheck "Block all public access" (we need public read access for uploaded files)
   - **Bucket Versioning**: Enable if desired
   - **Server-side encryption**: Enable with Amazon S3 managed keys (SSE-S3)

## Step 2: Configure Bucket Policy

Add the following bucket policy to allow public read access to uploaded files:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "PublicReadGetObject",
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        }
    ]
}
```

Replace `your-bucket-name` with your actual bucket name.

## Step 3: Create IAM User

1. Navigate to IAM service in AWS Console
2. Click "Users" → "Add user"
3. Enter username (e.g., `lms-s3-user`)
4. Select "Programmatic access"
5. Click "Next: Permissions"
6. Choose "Attach existing policies directly"
7. Search and select `AmazonS3FullAccess` policy
8. Complete user creation
9. **Important**: Save the Access Key ID and Secret Access Key

## Step 4: Configure Environment Variables

Update your `.env` file with the following AWS S3 configuration:

```env
# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_id
AWS_SECRET_ACCESS_KEY=your_secret_access_key
AWS_S3_BUCKET_NAME=your_bucket_name
```

## Step 5: Folder Structure

The application will automatically create the following folder structure in your S3 bucket:

```
your-bucket/
├── avatars/           # User profile pictures
├── courses/
│   ├── thumbnails/    # Course thumbnail images
│   ├── videos/        # Course lecture videos
│   └── materials/     # Course materials (PDFs, etc.)
└── blogs/
    └── thumbnails/    # Blog post images
```

## Security Best Practices

1. **Use IAM Roles**: For production, consider using IAM roles instead of access keys
2. **Least Privilege**: Create a custom IAM policy with minimal required permissions
3. **Bucket Encryption**: Enable server-side encryption
4. **Access Logging**: Enable S3 access logging for audit purposes
5. **Versioning**: Enable versioning to protect against accidental deletions

## Custom IAM Policy (Recommended)

Instead of using `AmazonS3FullAccess`, create a custom policy with minimal permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name"
        }
    ]
}
```

## Testing

After setup, test the configuration by:

1. Starting your application
2. Uploading a user avatar
3. Creating a course with thumbnail
4. Checking that files appear in your S3 bucket

## Troubleshooting

### Common Issues:

1. **Access Denied**: Check IAM permissions and bucket policy
2. **Bucket Not Found**: Verify bucket name and region in environment variables
3. **Invalid Credentials**: Ensure Access Key ID and Secret Access Key are correct
4. **CORS Issues**: If accessing from frontend, configure CORS on your bucket

### CORS Configuration (if needed):

```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": []
    }
]
```

## Migration from Cloudinary

If you're migrating from Cloudinary:

1. The database schema remains the same (public_id and secure_url fields)
2. `public_id` now stores the S3 object key
3. `secure_url` now stores the S3 object URL
4. Existing Cloudinary URLs in your database will need to be migrated manually if needed

## Cost Optimization

1. **Lifecycle Policies**: Set up lifecycle policies to move old files to cheaper storage classes
2. **Monitoring**: Use AWS CloudWatch to monitor usage and costs
3. **Cleanup**: Implement cleanup for temporary files and failed uploads
