import Blog from '../models/blog.model.js';
import { uploadToS3, deleteFromS3, cleanupLocalFile } from '../utils/s3.utils.js';
import fs from 'fs/promises';
import AppError from '../utils/error.utils.js';

export const createBlog = async (req, res, next) => {
    try {
        const { title, description, link } = req.body;

        if (!title || !description || !link) {
            return next(new AppError("All fields are required", 400));
        }

        if (!req.file) {
            return next(new AppError("Thumbnail image is required", 400));
        }

        // Upload to S3
        const s3Result = await uploadToS3(req.file.path, 'blogs/thumbnails');

        // Create blog with S3 details
        const blog = await Blog.create({
            title,
            description,
            link,
            imageUrl: req.file.path,
            thumbnail: {
                public_id: s3Result.key,
                secure_url: s3Result.url
            }
        });

        // Clean up local file
        cleanupLocalFile(req.file.path);

        res.status(201).json({
            success: true,
            message: "Blog created successfully",
            blog
        });

    } catch (error) {
        // Remove local file in case of error
        try {
            if (req.file && req.file.path && await fs.access(req.file.path).then(() => true).catch(() => false)) {
                await fs.unlink(req.file.path);
            }
        } catch (cleanupError) {
            console.warn('Failed to cleanup file after error:', cleanupError.message);
        }
        return next(new AppError(error.message, 500));
    }
};

export const getAllBlogs = async (req, res, next) => {
    try {
        const blogs = await Blog.find({}).sort({ createdAt: -1 });
        res.status(200).json({
            success: true,
            blogs
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

export const deleteBlog = async (req, res, next) => {
    try {
        const { id } = req.params;

        const blog = await Blog.findById(id);

        if (!blog) {
            return next(new AppError("Blog not found", 404));
        }

        // Delete image from S3
        if (blog.thumbnail && blog.thumbnail.public_id) {
            await deleteFromS3(blog.thumbnail.public_id);
        }

        // Delete blog from database
        await Blog.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: "Blog deleted successfully"
        });

    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};

export const getBlogById = async (req, res, next) => {
    try {
        const { id } = req.params;
        const blog = await Blog.findById(id);

        if (!blog) {
            return next(new AppError("Blog not found", 404));
        }

        res.status(200).json({
            success: true,
            blog
        });
    } catch (error) {
        return next(new AppError(error.message, 500));
    }
};