import dotenv from 'dotenv';
import { S3Client, ListBucketsCommand, HeadBucketCommand } from '@aws-sdk/client-s3';

// Load environment variables
dotenv.config();

async function testAWSCredentials() {
    console.log('🧪 Testing AWS Credentials...\n');
    
    // Check if all required environment variables are set
    const requiredVars = ['AWS_REGION', 'AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_S3_BUCKET_NAME'];
    const missing = requiredVars.filter(varName => !process.env[varName]);
    
    if (missing.length > 0) {
        console.error('❌ Missing environment variables:', missing);
        return;
    }
    
    console.log('✅ Environment variables found:');
    console.log(`   AWS_REGION: ${process.env.AWS_REGION}`);
    console.log(`   AWS_ACCESS_KEY_ID: ${process.env.AWS_ACCESS_KEY_ID}`);
    console.log(`   AWS_SECRET_ACCESS_KEY: ${process.env.AWS_SECRET_ACCESS_KEY ? '***' + process.env.AWS_SECRET_ACCESS_KEY.slice(-4) : 'Not set'}`);
    console.log(`   AWS_S3_BUCKET_NAME: ${process.env.AWS_S3_BUCKET_NAME}\n`);
    
    try {
        // Create S3 client
        const s3Client = new S3Client({
            region: process.env.AWS_REGION,
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            },
        });
        
        console.log('🔧 S3 Client created successfully\n');
        
        // Test 1: List buckets (basic credential test)
        console.log('📋 Test 1: Listing buckets...');
        try {
            const listCommand = new ListBucketsCommand({});
            const listResult = await s3Client.send(listCommand);
            console.log('✅ Successfully listed buckets:');
            listResult.Buckets?.forEach(bucket => {
                console.log(`   - ${bucket.Name} (Created: ${bucket.CreationDate})`);
            });
            console.log();
        } catch (error) {
            console.error('❌ Failed to list buckets:', error.message);
            console.error('   This usually indicates invalid credentials or insufficient permissions\n');
            return;
        }
        
        // Test 2: Check specific bucket
        console.log(`🪣 Test 2: Checking bucket '${process.env.AWS_S3_BUCKET_NAME}'...`);
        try {
            const headCommand = new HeadBucketCommand({
                Bucket: process.env.AWS_S3_BUCKET_NAME
            });
            await s3Client.send(headCommand);
            console.log('✅ Bucket exists and is accessible\n');
        } catch (error) {
            console.error('❌ Failed to access bucket:', error.message);
            if (error.name === 'NotFound') {
                console.error('   The bucket does not exist or is in a different region');
            } else if (error.name === 'Forbidden') {
                console.error('   Access denied - check bucket permissions');
            }
            console.log();
            return;
        }
        
        console.log('🎉 All AWS credential tests passed!');
        console.log('✅ Your AWS S3 configuration is working correctly');
        
    } catch (error) {
        console.error('❌ AWS Configuration Error:', error.message);
        
        // Provide specific error guidance
        if (error.message.includes('credential')) {
            console.error('\n💡 Credential Issues:');
            console.error('   1. Verify your AWS Access Key ID and Secret Access Key');
            console.error('   2. Make sure the credentials are not expired');
            console.error('   3. Check if the IAM user has S3 permissions');
        }
        
        if (error.message.includes('region')) {
            console.error('\n💡 Region Issues:');
            console.error('   1. Make sure AWS_REGION matches your bucket region');
            console.error('   2. Common regions: us-east-1, us-west-2, eu-west-1, eu-north-1');
        }
    }
}

// Run the test
testAWSCredentials().catch(console.error);
