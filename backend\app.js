import cors from 'cors';
import cookieParser from 'cookie-parser';
import morgan from 'morgan';
import userRoutes from './routes/user.routes.js';
import courseRoutes from './routes/course.routes.js';
import paymentRoutes from './routes/payment.routes.js';
import coursePurchaseRoutes from './routes/coursePurchase.routes.js';
import testSubmissionRoutes from './routes/testSubmission.routes.js';
import miscellaneousRoutes from './routes/miscellaneous.routes.js';
import express from 'express';
import errorMiddleware from './middleware/error.middleware.js';
import blogRoutes from './routes/blog.routes.js'
import { initializeDirectories } from './utils/fileSystem.utils.js';
import { performHealthCheck } from './utils/healthCheck.utils.js';
import { generalLimiter } from './middleware/rateLimiter.middleware.js';

const app = express();

const CLIENT_URLS = process.env.CLIENT_URL
console.log(CLIENT_URLS)

// These are now handled below with size limits
app.use(cookieParser());
app.use(morgan('dev'));

// Apply rate limiting
app.use(generalLimiter);

app.use(cors({
    origin: CLIENT_URLS,
    methods: ['GET', 'POST','DELETE','PUT'],
    credentials: true,
}));


// Request size limits and security
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        const healthReport = await performHealthCheck();
        const statusCode = healthReport.status === 'healthy' ? 200 :
                          healthReport.status === 'degraded' ? 200 : 503;

        res.status(statusCode).json({
            success: healthReport.status === 'healthy' || healthReport.status === 'degraded',
            ...healthReport
        });
    } catch (error) {
        res.status(503).json({
            success: false,
            status: 'error',
            message: 'Health check failed',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

app.get('/', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'LMS API Server is running',
        version: '1.0.0',
        timestamp: new Date().toISOString()
    });
});

// API routes
app.use('/api/v1/user', userRoutes);
app.use('/api/v1/courses', courseRoutes);
app.use('/api/v1/payments', paymentRoutes);
app.use('/api/v1/course-purchase', coursePurchaseRoutes);
app.use('/api/v1/test-submission', testSubmissionRoutes);
app.use('/api/v1/blog', blogRoutes);
app.use('/api/v1/', miscellaneousRoutes);

// 404 handler for undefined routes
app.all('*', (req, res, next) => {
    const error = new Error(`Route ${req.originalUrl} not found`);
    error.statusCode = 404;
    next(error);
});

// Global error handling middleware (must be last)
app.use(errorMiddleware);

// Initialize file system directories
initializeDirectories();

// Note: Database connection and health monitoring are initialized in server.js
// after environment variables are loaded

export default app;