import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure AWS S3 client
const s3Client = new S3Client({
    region: process.env.AWS_REGION,
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
});

/**
 * Upload file to S3 bucket
 * @param {string} filePath - Local file path
 * @param {string} folder - S3 folder/prefix
 * @param {string} fileName - Optional custom file name
 * @returns {Object} - Upload result with key and url
 */
export const uploadToS3 = async (filePath, folder = '', fileName = null) => {
    try {
        // Validate file exists
        if (!fs.existsSync(filePath)) {
            throw new Error('File not found');
        }

        // Read file
        const fileContent = fs.readFileSync(filePath);
        
        // Generate unique file name if not provided
        const originalName = fileName || path.basename(filePath);
        const timestamp = Date.now();
        const uniqueFileName = `${timestamp}-${originalName}`;
        
        // Create S3 key (path in bucket)
        const key = folder ? `${folder}/${uniqueFileName}` : uniqueFileName;
        
        // Determine content type
        const ext = path.extname(originalName).toLowerCase();
        let contentType = 'application/octet-stream';
        
        const mimeTypes = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.mp4': 'video/mp4',
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        };
        
        if (mimeTypes[ext]) {
            contentType = mimeTypes[ext];
        }

        // Upload to S3
        const uploadParams = {
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
            Body: fileContent,
            ContentType: contentType,
            ACL: 'public-read', // Make files publicly accessible
        };

        const command = new PutObjectCommand(uploadParams);
        await s3Client.send(command);

        // Generate public URL
        const url = `https://${process.env.AWS_S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

        return {
            key: key,
            url: url,
            bucket: process.env.AWS_S3_BUCKET_NAME
        };

    } catch (error) {
        console.error('S3 upload error:', error);
        throw new Error(`Failed to upload file to S3: ${error.message}`);
    }
};

/**
 * Delete file from S3 bucket
 * @param {string} key - S3 object key
 * @returns {boolean} - Success status
 */
export const deleteFromS3 = async (key) => {
    try {
        if (!key) {
            console.warn('No S3 key provided for deletion');
            return true; // Don't throw error for missing key
        }

        const deleteParams = {
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
        };

        const command = new DeleteObjectCommand(deleteParams);
        await s3Client.send(command);
        
        console.log(`Successfully deleted ${key} from S3`);
        return true;

    } catch (error) {
        console.error('S3 delete error:', error);
        // Don't throw error for delete operations to prevent blocking other operations
        return false;
    }
};

/**
 * Generate presigned URL for private file access
 * @param {string} key - S3 object key
 * @param {number} expiresIn - URL expiration time in seconds (default: 1 hour)
 * @returns {string} - Presigned URL
 */
export const getPresignedUrl = async (key, expiresIn = 3600) => {
    try {
        const command = new GetObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
        });

        const url = await getSignedUrl(s3Client, command, { expiresIn });
        return url;

    } catch (error) {
        console.error('S3 presigned URL error:', error);
        throw new Error(`Failed to generate presigned URL: ${error.message}`);
    }
};

/**
 * Extract S3 key from URL
 * @param {string} url - S3 URL
 * @returns {string} - S3 key
 */
export const extractS3Key = (url) => {
    if (!url) return null;
    
    try {
        // Handle different S3 URL formats
        if (url.includes('.s3.')) {
            // Format: https://bucket.s3.region.amazonaws.com/key
            const urlParts = url.split('.s3.');
            if (urlParts.length > 1) {
                const pathPart = urlParts[1].split('/').slice(1).join('/');
                return pathPart;
            }
        } else if (url.includes('s3.amazonaws.com')) {
            // Format: https://s3.amazonaws.com/bucket/key
            const urlParts = url.split('s3.amazonaws.com/');
            if (urlParts.length > 1) {
                const pathParts = urlParts[1].split('/');
                return pathParts.slice(1).join('/'); // Remove bucket name
            }
        }
        
        return null;
    } catch (error) {
        console.error('Error extracting S3 key:', error);
        return null;
    }
};

/**
 * Clean up local file after upload
 * @param {string} filePath - Local file path to delete
 */
export const cleanupLocalFile = (filePath) => {
    try {
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`Cleaned up local file: ${filePath}`);
        }
    } catch (error) {
        console.error('Error cleaning up local file:', error);
    }
};

export default s3Client;
