import dotenv from 'dotenv';
import { S3Client, HeadBucketCommand } from '@aws-sdk/client-s3';

// Load environment variables
dotenv.config();

async function findBucketRegion() {
    console.log('🔍 Finding correct region for bucket...\n');
    
    const bucketName = process.env.AWS_S3_BUCKET_NAME;
    console.log(`Bucket name: ${bucketName}`);
    console.log(`Current region setting: ${process.env.AWS_REGION}\n`);
    
    // Common AWS regions to test
    const regions = [
        'us-east-1',      // US East (N. Virginia)
        'us-east-2',      // US East (Ohio)
        'us-west-1',      // US West (N. California)
        'us-west-2',      // US West (Oregon)
        'eu-west-1',      // Europe (Ireland)
        'eu-west-2',      // Europe (London)
        'eu-west-3',      // Europe (Paris)
        'eu-central-1',   // Europe (Frankfurt)
        'eu-north-1',     // Europe (Stockholm)
        'ap-south-1',     // Asia Pacific (Mumbai)
        'ap-southeast-1', // Asia Pacific (Singapore)
        'ap-southeast-2', // Asia Pacific (Sydney)
        'ap-northeast-1', // Asia Pacific (Tokyo)
        'ap-northeast-2', // Asia Pacific (Seoul)
        'ca-central-1',   // Canada (Central)
        'sa-east-1'       // South America (São Paulo)
    ];
    
    for (const region of regions) {
        try {
            console.log(`Testing region: ${region}...`);
            
            const s3Client = new S3Client({
                region: region,
                credentials: {
                    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
                    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
                },
            });
            
            const headCommand = new HeadBucketCommand({ Bucket: bucketName });
            await s3Client.send(headCommand);
            
            console.log(`\n🎉 FOUND IT! Your bucket '${bucketName}' is in region: ${region}`);
            console.log(`\n✅ Update your .env file:`);
            console.log(`AWS_REGION=${region}`);
            return region;
            
        } catch (error) {
            if (error.name === 'NoSuchBucket') {
                console.log(`   ❌ Bucket doesn't exist in ${region}`);
            } else if (error.name === 'Forbidden' || error.name === 'AccessDenied') {
                console.log(`   🔒 Access denied in ${region} (bucket might exist but no permissions)`);
            } else if (error.name === 'UnknownError' && error.$metadata?.httpStatusCode === 301) {
                console.log(`   🔄 Redirect from ${region} (bucket is in different region)`);
            } else {
                console.log(`   ⚠️  Error in ${region}: ${error.message}`);
            }
        }
    }
    
    console.log(`\n❌ Could not find bucket '${bucketName}' in any tested region.`);
    console.log('\n💡 Possible solutions:');
    console.log('1. Check if the bucket name is correct');
    console.log('2. Create the bucket in AWS S3 console');
    console.log('3. Make sure your AWS credentials have access to the bucket');
}

// Run the test
findBucketRegion().catch(console.error);
