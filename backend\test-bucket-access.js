import dotenv from 'dotenv';
import { S3Client, HeadBucketCommand, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

// Load environment variables
dotenv.config();

async function testBucketAccess() {
    console.log('🧪 Testing S3 Bucket Access...\n');
    
    const s3Client = new S3Client({
        region: process.env.AWS_REGION,
        credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        },
    });
    
    const bucketName = process.env.AWS_S3_BUCKET_NAME;
    
    try {
        // Test 1: Check if bucket exists and is accessible
        console.log(`🪣 Test 1: Checking bucket '${bucketName}'...`);
        const headCommand = new HeadBucketCommand({ Bucket: bucketName });
        await s3Client.send(headCommand);
        console.log('✅ Bucket exists and is accessible\n');
        
        // Test 2: Try to upload a test file
        console.log('📤 Test 2: Testing file upload...');
        const testKey = 'test/credential-test.txt';
        const testContent = 'This is a test file to verify S3 upload permissions.';
        
        const putCommand = new PutObjectCommand({
            Bucket: bucketName,
            Key: testKey,
            Body: testContent,
            ContentType: 'text/plain',
            ACL: 'public-read'
        });
        
        const uploadResult = await s3Client.send(putCommand);
        console.log('✅ File uploaded successfully!');
        console.log(`   ETag: ${uploadResult.ETag}`);
        console.log(`   URL: https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${testKey}\n`);
        
        // Test 3: Clean up test file
        console.log('🧹 Test 3: Cleaning up test file...');
        const deleteCommand = new DeleteObjectCommand({
            Bucket: bucketName,
            Key: testKey
        });
        
        await s3Client.send(deleteCommand);
        console.log('✅ Test file deleted successfully\n');
        
        console.log('🎉 All bucket access tests passed!');
        console.log('✅ Your S3 configuration is working correctly for file operations');
        
    } catch (error) {
        console.error('❌ Bucket Access Error:', error.message);
        console.error('Error Code:', error.name);
        
        if (error.name === 'NoSuchBucket') {
            console.error('\n💡 Solutions:');
            console.error('   1. Create the bucket in AWS S3 console');
            console.error(`   2. Make sure the bucket name '${bucketName}' is correct`);
            console.error(`   3. Ensure the bucket is in region '${process.env.AWS_REGION}'`);
        } else if (error.name === 'AccessDenied' || error.name === 'Forbidden') {
            console.error('\n💡 Permission Issues:');
            console.error('   1. The IAM user needs these S3 permissions:');
            console.error('      - s3:GetObject');
            console.error('      - s3:PutObject');
            console.error('      - s3:DeleteObject');
            console.error('      - s3:PutObjectAcl');
            console.error(`   2. For bucket: ${bucketName}`);
        } else if (error.message.includes('credential')) {
            console.error('\n💡 Credential Issues:');
            console.error('   1. Double-check your AWS Access Key ID and Secret');
            console.error('   2. Make sure credentials are not expired');
        }
    }
}

// Run the test
testBucketAccess().catch(console.error);
