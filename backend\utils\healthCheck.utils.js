import mongoose from 'mongoose';
import { razorpay } from '../server.js';
import { v2 as cloudinary } from 'cloudinary';

/**
 * Check database connection health
 */
export const checkDatabaseHealth = async () => {
    try {
        const state = mongoose.connection.readyState;
        const states = {
            0: 'disconnected',
            1: 'connected',
            2: 'connecting',
            3: 'disconnecting'
        };

        if (state === 1) {
            // Test with a simple query
            await mongoose.connection.db.admin().ping();
            return {
                status: 'healthy',
                state: states[state],
                host: mongoose.connection.host,
                name: mongoose.connection.name
            };
        } else {
            return {
                status: 'unhealthy',
                state: states[state],
                error: 'Database not connected'
            };
        }
    } catch (error) {
        return {
            status: 'unhealthy',
            error: error.message
        };
    }
};

/**
 * Check Cloudinary service health
 */
export const checkCloudinaryHealth = async () => {
    try {
        // Test Cloudinary connection by getting account details
        const result = await cloudinary.api.ping();
        return {
            status: 'healthy',
            service: 'cloudinary',
            response: result
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            service: 'cloudinary',
            error: error.message
        };
    }
};

/**
 * Check Razorpay service health
 */
export const checkRazorpayHealth = async () => {
    try {
        if (!razorpay) {
            return {
                status: 'disabled',
                service: 'razorpay',
                message: 'Razorpay not configured'
            };
        }

        // Test Razorpay connection by fetching plans
        const plans = await razorpay.plans.all({ count: 1 });
        return {
            status: 'healthy',
            service: 'razorpay',
            plansCount: plans.count
        };
    } catch (error) {
        return {
            status: 'unhealthy',
            service: 'razorpay',
            error: error.message
        };
    }
};

/**
 * Check system memory usage
 */
export const checkMemoryUsage = () => {
    const usage = process.memoryUsage();
    const formatBytes = (bytes) => {
        return Math.round(bytes / 1024 / 1024 * 100) / 100; // MB
    };

    return {
        rss: formatBytes(usage.rss), // Resident Set Size
        heapTotal: formatBytes(usage.heapTotal),
        heapUsed: formatBytes(usage.heapUsed),
        external: formatBytes(usage.external),
        unit: 'MB'
    };
};

/**
 * Check environment variables
 */
export const checkEnvironmentHealth = () => {
    const requiredVars = [
        'PORT',
        'MONGO_URI',
        'JWT_SECRET',
        'JWT_EXPIRY',
        'CLOUDINARY_CLOUD_NAME',
        'CLOUDINARY_API_KEY',
        'CLOUDINARY_API_SECRET',
        'CLIENT_URL'
    ];

    const optionalVars = [
        'RAZORPAY_KEY_ID',
        'RAZORPAY_SECRET',
        'SMTP_HOST',
        'SMTP_USERNAME',
        'SMTP_PASSWORD'
    ];

    const missing = requiredVars.filter(varName => !process.env[varName]);
    const optional = optionalVars.filter(varName => !process.env[varName]);

    return {
        status: missing.length === 0 ? 'healthy' : 'unhealthy',
        required: {
            total: requiredVars.length,
            present: requiredVars.length - missing.length,
            missing: missing
        },
        optional: {
            total: optionalVars.length,
            present: optionalVars.length - optional.length,
            missing: optional
        }
    };
};

/**
 * Comprehensive health check
 */
export const performHealthCheck = async () => {
    const startTime = Date.now();

    try {
        const [database, cloudinary, razorpay] = await Promise.allSettled([
            checkDatabaseHealth(),
            checkCloudinaryHealth(),
            checkRazorpayHealth()
        ]);

        const memory = checkMemoryUsage();
        const environment = checkEnvironmentHealth();

        const healthReport = {
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            responseTime: Date.now() - startTime,
            status: 'healthy', // Will be updated based on checks
            services: {
                database: database.status === 'fulfilled' ? database.value : { status: 'error', error: database.reason },
                cloudinary: cloudinary.status === 'fulfilled' ? cloudinary.value : { status: 'error', error: cloudinary.reason },
                razorpay: razorpay.status === 'fulfilled' ? razorpay.value : { status: 'error', error: razorpay.reason }
            },
            system: {
                memory,
                environment,
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch
            }
        };

        // Determine overall health status
        const criticalServices = ['database'];
        const hasCriticalIssues = criticalServices.some(service =>
            healthReport.services[service].status === 'unhealthy' ||
            healthReport.services[service].status === 'error'
        );

        if (hasCriticalIssues || environment.status === 'unhealthy') {
            healthReport.status = 'unhealthy';
        } else {
            const hasWarnings = Object.values(healthReport.services).some(service =>
                service.status === 'unhealthy' || service.status === 'error'
            );
            healthReport.status = hasWarnings ? 'degraded' : 'healthy';
        }

        return healthReport;
    } catch (error) {
        return {
            timestamp: new Date().toISOString(),
            status: 'error',
            error: error.message,
            responseTime: Date.now() - startTime
        };
    }
};

/**
 * Log health status periodically
 */
export const startHealthMonitoring = (intervalMinutes = 5) => {
    const interval = intervalMinutes * 60 * 1000;

    setInterval(async () => {
        try {
            const health = await performHealthCheck();

            if (health.status === 'unhealthy' || health.status === 'error') {
                console.error('🚨 Health Check Alert:', {
                    status: health.status,
                    timestamp: health.timestamp,
                    issues: health.error || 'Check services for details'
                });
            } else {
                console.log(`💚 Health Check: ${health.status} - Uptime: ${Math.round(health.uptime)}s`);
            }
        } catch (error) {
            console.error('❌ Health monitoring error:', error.message);
        }
    }, interval);

    console.log(`🏥 Health monitoring started (checking every ${intervalMinutes} minutes)`);
};
