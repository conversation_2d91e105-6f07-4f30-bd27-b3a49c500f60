import courseModel from '../models/course.model.js'
import CoursePurchase from '../models/coursePurchase.model.js';
import AppError from '../utils/error.utils.js';
import { uploadToS3, deleteFromS3, cleanupLocalFile } from '../utils/s3.utils.js';
import fs from 'fs';

// get all courses
const getAllCourses = async (req, res, next) => {
    try {
        const courses = await courseModel.find({}).select('-lectures');

        res.status(200).json({
            success: true,
            message: 'All courses',
            courses
        })
    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}

// get specific course
const getLecturesByCourseId = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { role, id: userId } = req.user;

        const course = await courseModel.findById(id);
        if (!course) {
            return next(new AppError('course not found', 404));
        }

        // Check if user has access to this course
        let hasAccess = false;
        let hasPurchased = false;

        if (role === 'ADMIN') {
            hasAccess = true;
        } else {
            // Check if user has purchased this course
            const purchase = await CoursePurchase.findOne({
                userId,
                courseId: id,
                paymentStatus: 'completed'
            });

            if (purchase) {
                hasAccess = true;
                hasPurchased = true;
            }
        }

        // If user doesn't have access, return limited course info
        if (!hasAccess) {
            const limitedCourse = {
                _id: course._id,
                title: course.title,
                description: course.description,
                category: course.category,
                thumbnail: course.thumbnail,
                createdBy: course.createdBy,
                numberOfLectures: course.numberOfLectures,
                price: course.price,
                lectures: course.lectures.map(lecture => ({
                    _id: lecture._id,
                    title: lecture.title,
                    description: lecture.description
                    // Don't include video links, materials, or questions
                }))
            };

            return res.status(200).json({
                success: true,
                message: 'Course preview (purchase required for full access)',
                course: limitedCourse,
                hasAccess: false,
                hasPurchased: false
            });
        }

        // User has access, return full course
        res.status(200).json({
            success: true,
            message: 'Course with full access',
            course,
            hasAccess: true,
            hasPurchased
        });
    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}

// create course
const createCourse = async (req, res, next) => {
    try {
        const { title, description, category, createdBy, price } = req.body;

        if (!title || !description || !category || !createdBy || price === undefined) {
            return next(new AppError('All fields including price are required', 400));
        }

        if (price < 0) {
            return next(new AppError('Price cannot be negative', 400));
        }

        const course = await courseModel.create({
            title,
            description,
            category,
            createdBy,
            price
        })

        if (!course) {
            return next(new AppError('Course could not created, please try again', 500));
        }

        // file upload
        if (req.file) {
            const result = await uploadToS3(req.file.path, 'courses/thumbnails');

            if (result) {
                course.thumbnail.public_id = result.key;
                course.thumbnail.secure_url = result.url;
            }

            cleanupLocalFile(req.file.path);
        }

        await course.save();

        res.status(200).json({
            success: true,
            message: 'Course successfully created',
            course
        })

    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}

// update course
const updateCourse = async (req, res, next) => {
    try {
        const { id } = req.params;
        const course = await courseModel.findByIdAndUpdate(
            id,
            {
                $set: req.body
            },
            {
                runValidators: true
            }
        )

        if (!course) {
            return next(new AppError('Course with given id does not exist', 500));
        }

        if (req.file) {
            // Delete old thumbnail if it exists
            if (course.thumbnail && course.thumbnail.public_id) {
                await deleteFromS3(course.thumbnail.public_id);
            }

            const result = await uploadToS3(req.file.path, 'courses/thumbnails');

            if (result) {
                course.thumbnail.public_id = result.key;
                course.thumbnail.secure_url = result.url;
            }

            cleanupLocalFile(req.file.path);
        }

        await course.save();

        res.status(200).json({
            success: true,
            message: 'Course updated successfully',
            course
        })
    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}

// remove course
const removeCourse = async (req, res, next) => {
    try {
        const { id } = req.params;

        const course = await courseModel.findById(id);

        if (!course) {
            return next(new AppError('Course with given id does not exist', 500));
        }

        await courseModel.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: 'course deleted successfully'
        })

    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}

// delete lecture by course id and lecture id

const addLectureToCourseById = async (req, res, next) => {
    try {
        const { title, description, link, videoURL } = req.body;
        const { id } = req.params;

        // Parse questions from FormData
        let questions = [];
        if (req.body.questions) {
            try {
                questions = JSON.parse(req.body.questions);
            } catch (error) {
                return next(new AppError('Invalid questions format', 400));
            }
        }

        // Validate basic lecture details
        if (!title || !description || !link) {
            return next(new AppError('all fields are required', 500));
        }

        // Validate questions format if provided
        if (questions && questions.length > 0) {
            const isValidQuestions = questions.every(question => {
                return (
                    question.questionText &&
                    Array.isArray(question.options) &&
                    question.options.length >= 2 &&
                    typeof question.correctOption === 'number' &&
                    question.correctOption >= 0 &&
                    question.correctOption < question.options.length
                );
            });

            if (!isValidQuestions) {
                return next(new AppError('Invalid question format. Each question must have questionText, options array, and valid correctOption', 400));
            }
        }

        const course = await courseModel.findById(id);

        if (!course) {
            return next(new AppError('course with given id does not exist', 500));
        }

        const lectureData = {
            title,
            description,
            link,
            videoURL: videoURL || '',
            videoFile: {},
            lecture: {},
            materials: {},
            questions // Add the parsed questions array
        }

        // Handle video content - prioritize YouTube URL over file upload
        if (videoURL && videoURL.trim()) {
            // Validate YouTube embed URL format
            const youtubeEmbedRegex = /^https:\/\/www\.youtube\.com\/embed\/[a-zA-Z0-9_-]+/;
            if (!youtubeEmbedRegex.test(videoURL.trim())) {
                return next(new AppError('Invalid YouTube embed URL format. Please use: https://www.youtube.com/embed/VIDEO_ID', 400));
            }
            lectureData.videoURL = videoURL.trim();
        } else if (req.files && req.files.lecture) {
            // Upload video file to S3 if no YouTube URL provided
            try {
                const result = await uploadToS3(req.files.lecture[0].path, 'courses/videos');
                if (result) {
                    lectureData.videoFile.public_id = result.key;
                    lectureData.videoFile.secure_url = result.url;
                    // Keep backward compatibility
                    lectureData.lecture.public_id = result.key;
                    lectureData.lecture.secure_url = result.url;
                }

                cleanupLocalFile(req.files.lecture[0].path);
            } catch (e) {
                return next(new AppError(e.message, 500));
            }
        } else {
            return next(new AppError('Please provide either a YouTube embed URL or upload a video file', 400));
        }

        // PDF upload
        if (req.files && req.files.pdf) {
            try {
                const result = await uploadToS3(req.files.pdf[0].path, 'courses/materials');
                if (result) {
                    lectureData.materials.public_id = result.key;
                    lectureData.materials.secure_url = result.url;
                }

                cleanupLocalFile(req.files.pdf[0].path);
            } catch (e) {
                return next(new AppError(e.message, 500));
            }
        }

        course.lectures.push(lectureData);
        course.numberOfLectures = course.lectures.length;

        await course.save();

        res.status(200).json({
            success: true,
            message: 'lecture and questions added successfully'
        });

    } catch (e) {
        return next(new AppError(e.message, 500));
    }
}



const deleteCourseLecture = async (req, res, next) => {
    try {
        const { courseId, lectureId } = req.query;

        const course = await courseModel.findById(courseId);

        if (!course) {
            return next(new AppError('Course not found', 404));
        }

        const lectureIndex = course.lectures.findIndex(lecture => lecture._id.toString() === lectureId);

        if (lectureIndex === -1) {
            return next(new AppError('Lecture not found in the course', 404));
        }

        course.lectures.splice(lectureIndex, 1);

        course.numberOfLectures = course.lectures.length;

        await course.save();

        res.status(200).json({
            success: true,
            message: 'Lecture deleted successfully'
        });
    } catch (e) {
        return next(new AppError(e.message, 500));
    }
};


// update lecture by course id and lecture id
const updateCourseLecture = async (req, res, next) => {
    try {
        const { courseId, lectureId } = req.query;
        const { title, description } = req.body;

        if (!title || !description) {
            return next(new AppError('All fields are required', 400));
        }

        const course = await courseModel.findById(courseId);

        if (!course) {
            return next(new AppError('Course not found', 404));
        }

        const lectureIndex = course.lectures.findIndex(lecture => lecture._id.toString() === lectureId);

        if (lectureIndex === -1) {
            return next(new AppError('Lecture not found in the course', 404));
        }

        const updatedLectureData = {
            title,
            description,
            lecture: {
                public_id: course.lectures[lectureIndex].lecture.public_id,
                secure_url: course.lectures[lectureIndex].lecture.secure_url
            }
        };

        if (req.file) {
            try {
                const result = await uploadToS3(req.file.path, 'courses/videos');
                if (result) {
                    updatedLectureData.lecture.public_id = result.key;
                    updatedLectureData.lecture.secure_url = result.url;
                }

                // If there's an existing video, delete the old one from S3
                if (course.lectures[lectureIndex].lecture.public_id) {
                    await deleteFromS3(course.lectures[lectureIndex].lecture.public_id);
                }

                cleanupLocalFile(req.file.path);
            } catch (e) {
                return next(new AppError(e.message, 500));
            }
        }

        // Update the lecture details
        course.lectures[lectureIndex] = updatedLectureData;

        await course.save();

        res.status(200).json({
            success: true,
            message: 'Lecture updated successfully'
        });
    } catch (e) {
        return next(new AppError(e.message, 500));
    }
};









export {
    getAllCourses,
    getLecturesByCourseId,
    createCourse,
    updateCourse,
    removeCourse,
    addLectureToCourseById,
    deleteCourseLecture,
    updateCourseLecture,
}