PORT=5000
MONGO_URI=mongodb+srv://username:<EMAIL>/database_name

JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=1d

# AWS S3 Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=your_s3_bucket_name

RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_SECRET=your_razorpay_secret
RAZORPAY_PLAN_ID=your_razorpay_plan_id

CLIENT_URL=http://localhost:5173
FRONTEND_URL=http://localhost:5173

SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM_EMAIL=<EMAIL>

CONTACT_US_EMAIL=<EMAIL>
