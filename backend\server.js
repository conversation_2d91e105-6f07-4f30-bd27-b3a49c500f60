import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables FIRST
const envPath = path.join(__dirname, '.env');
console.log('Loading .env from:', envPath);
const result = dotenv.config({ path: envPath });
if (result.error) {
    console.error('Error loading .env file:', result.error);
} else {
    console.log('✅ Environment variables loaded successfully');
}
console.log('MONGO_URI loaded:', !!process.env.MONGO_URI);

import app from "./app.js";
import Razorpay from "razorpay";
import connectToDb from './config/db.config.js';
import { startHealthMonitoring } from './utils/healthCheck.utils.js';

// Validate required environment variables
const requiredEnvVars = [
  'PORT',
  'MONGO_URI',
  'JWT_SECRET',
  'JWT_EXPIRY',
  'AWS_REGION',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'AWS_S3_BUCKET_NAME',
  'CLIENT_URL'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

const PORT = process.env.PORT || 5000;

// Global error handlers to prevent crashes
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  // Graceful shutdown
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Graceful shutdown
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

// AWS S3 configuration validation
try {
  if (!process.env.AWS_REGION || !process.env.AWS_ACCESS_KEY_ID ||
      !process.env.AWS_SECRET_ACCESS_KEY || !process.env.AWS_S3_BUCKET_NAME) {
    throw new Error('Missing AWS S3 configuration');
  }
  console.log('AWS S3 configured successfully');
} catch (error) {
  console.error('Failed to configure AWS S3:', error);
  process.exit(1);
}

// razorpay configuration
let razorpay = null;
try {
  if (process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_SECRET) {
    razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_SECRET
    });
    console.log('Razorpay initialized successfully');
  } else {
    console.warn('Razorpay credentials missing - payment features will be disabled:', {
      keyId: !!process.env.RAZORPAY_KEY_ID,
      secret: !!process.env.RAZORPAY_SECRET
    });
  }
} catch (error) {
  console.error('Failed to initialize Razorpay:', error);
  // Don't exit - payment features will be disabled but server can still run
}

export { razorpay };

// Start server with error handling
const server = app.listen(PORT, () => {
    console.log(`🚀 Server started successfully at http://localhost:${PORT}`);
    console.log(`📅 Started at: ${new Date().toISOString()}`);
});

// Handle server errors
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ Port ${PORT} is already in use`);
  } else {
    console.error('❌ Server error:', error);
  }
  process.exit(1);
});

// Initialize database connection after environment variables are loaded
connectToDb();

// Start health monitoring
startHealthMonitoring(5); // Check every 5 minutes