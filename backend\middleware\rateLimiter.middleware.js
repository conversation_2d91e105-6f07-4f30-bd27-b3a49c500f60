import rateLimit from 'express-rate-limit';

// General rate limiter for all routes
export const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per windowMs
    message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
    },
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    handler: (req, res) => {
        console.warn(`Rate limit exceeded for IP: ${req.ip} on route: ${req.originalUrl}`);
        res.status(429).json({
            success: false,
            message: 'Too many requests from this IP, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});

// Strict rate limiter for authentication routes
export const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 login attempts per windowMs
    message: {
        success: false,
        message: 'Too many authentication attempts, please try again later.',
    },
    skipSuccessfulRequests: true, // Don't count successful requests
    handler: (req, res) => {
        console.warn(`Auth rate limit exceeded for IP: ${req.ip} on route: ${req.originalUrl}`);
        res.status(429).json({
            success: false,
            message: 'Too many authentication attempts, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});

// Rate limiter for file uploads
export const uploadLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // Limit each IP to 10 uploads per hour
    message: {
        success: false,
        message: 'Too many file uploads, please try again later.',
    },
    handler: (req, res) => {
        console.warn(`Upload rate limit exceeded for IP: ${req.ip} on route: ${req.originalUrl}`);
        res.status(429).json({
            success: false,
            message: 'Too many file uploads, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});

// Rate limiter for password reset requests
export const passwordResetLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // Limit each IP to 3 password reset attempts per hour
    message: {
        success: false,
        message: 'Too many password reset attempts, please try again later.',
    },
    handler: (req, res) => {
        console.warn(`Password reset rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            success: false,
            message: 'Too many password reset attempts, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});

// Rate limiter for contact form
export const contactLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // Limit each IP to 5 contact form submissions per hour
    message: {
        success: false,
        message: 'Too many contact form submissions, please try again later.',
    },
    handler: (req, res) => {
        console.warn(`Contact form rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            success: false,
            message: 'Too many contact form submissions, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});

// Rate limiter for payment operations
export const paymentLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // Limit each IP to 10 payment operations per 15 minutes
    message: {
        success: false,
        message: 'Too many payment requests, please try again later.',
    },
    handler: (req, res) => {
        console.warn(`Payment rate limit exceeded for IP: ${req.ip} on route: ${req.originalUrl}`);
        res.status(429).json({
            success: false,
            message: 'Too many payment requests, please try again later.',
            retryAfter: Math.round(req.rateLimit.resetTime / 1000)
        });
    }
});
