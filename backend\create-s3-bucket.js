import dotenv from 'dotenv';
import { S3Client, CreateBucketCommand, PutBucketPolicyCommand, PutBucketCorsCommand } from '@aws-sdk/client-s3';

// Load environment variables
dotenv.config();

async function createS3Bucket() {
    console.log('🪣 Creating S3 Bucket for LMS...\n');
    
    const region = process.env.AWS_REGION || 'us-east-1';
    const bucketName = `lms-files-${Date.now()}`; // Unique bucket name
    
    console.log(`Region: ${region}`);
    console.log(`Bucket name: ${bucketName}\n`);
    
    const s3Client = new S3Client({
        region: region,
        credentials: {
            accessKeyId: process.env.AWS_ACCESS_KEY_ID,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
        },
    });
    
    try {
        // Step 1: Create the bucket
        console.log('📦 Step 1: Creating bucket...');
        
        const createParams = {
            Bucket: bucketName,
        };
        
        // For regions other than us-east-1, we need to specify the location constraint
        if (region !== 'us-east-1') {
            createParams.CreateBucketConfiguration = {
                LocationConstraint: region
            };
        }
        
        const createCommand = new CreateBucketCommand(createParams);
        await s3Client.send(createCommand);
        console.log('✅ Bucket created successfully!\n');
        
        // Step 2: Set bucket policy for public read access
        console.log('🔓 Step 2: Setting bucket policy for public read access...');
        
        const bucketPolicy = {
            Version: '2012-10-17',
            Statement: [
                {
                    Sid: 'PublicReadGetObject',
                    Effect: 'Allow',
                    Principal: '*',
                    Action: 's3:GetObject',
                    Resource: `arn:aws:s3:::${bucketName}/*`
                }
            ]
        };
        
        const policyCommand = new PutBucketPolicyCommand({
            Bucket: bucketName,
            Policy: JSON.stringify(bucketPolicy)
        });
        
        await s3Client.send(policyCommand);
        console.log('✅ Bucket policy set successfully!\n');
        
        // Step 3: Configure CORS
        console.log('🌐 Step 3: Configuring CORS...');
        
        const corsConfiguration = {
            CORSRules: [
                {
                    AllowedHeaders: ['*'],
                    AllowedMethods: ['GET', 'PUT', 'POST', 'DELETE'],
                    AllowedOrigins: ['*'],
                    ExposeHeaders: ['ETag'],
                    MaxAgeSeconds: 3000
                }
            ]
        };
        
        const corsCommand = new PutBucketCorsCommand({
            Bucket: bucketName,
            CORSConfiguration: corsConfiguration
        });
        
        await s3Client.send(corsCommand);
        console.log('✅ CORS configured successfully!\n');
        
        // Success message
        console.log('🎉 S3 Bucket setup completed successfully!\n');
        console.log('📝 Update your .env file with:');
        console.log(`AWS_S3_BUCKET_NAME=${bucketName}`);
        console.log(`AWS_REGION=${region}\n`);
        
        console.log('🔗 Bucket URL:');
        console.log(`https://${bucketName}.s3.${region}.amazonaws.com/\n`);
        
        console.log('📁 Your files will be organized as:');
        console.log(`├── avatars/           # User profile pictures`);
        console.log(`├── courses/`);
        console.log(`│   ├── thumbnails/    # Course thumbnails`);
        console.log(`│   ├── videos/        # Lecture videos`);
        console.log(`│   └── materials/     # PDFs and documents`);
        console.log(`└── blogs/`);
        console.log(`    └── thumbnails/    # Blog images`);
        
        return bucketName;
        
    } catch (error) {
        console.error('❌ Error creating bucket:', error.message);
        
        if (error.name === 'BucketAlreadyExists') {
            console.error('💡 The bucket name already exists. Bucket names must be globally unique.');
            console.error('   Try running the script again to generate a new unique name.');
        } else if (error.name === 'AccessDenied') {
            console.error('💡 Access denied. Make sure your IAM user has these permissions:');
            console.error('   - s3:CreateBucket');
            console.error('   - s3:PutBucketPolicy');
            console.error('   - s3:PutBucketCors');
        } else if (error.message.includes('credential')) {
            console.error('💡 Check your AWS credentials in the .env file');
        }
        
        throw error;
    }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
    createS3Bucket().catch(console.error);
}

export default createS3Bucket;
